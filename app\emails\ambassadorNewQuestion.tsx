import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Head,
  Heading,
  Html,
  Preview,
  Row,
  Section,
  Text,
} from "@react-email/components";

interface AmbassadorNewQuestionProps {
  ambassadorName: string;
  productName: string;
  answerPageUrl?: string;
}

export const AmbassadorNewQuestion = ({
  ambassadorName = "Ambassador",
  productName = "Our Product",
  answerPageUrl = "https://example.com/answer-question",
}: AmbassadorNewQuestionProps) => {
  return (
    <Html>
      <Head />
      <Preview>New question about {productName} - Your expertise is needed!</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Heading style={mainHeading}>
              New Question Alert
            </Heading>
          </Section>

          <Section style={content}>
            <Row style={boxInfos}>
              <Column>
                <Text style={paragraph}>
                  Hi {ambassadorName},
                </Text>
                <Text style={paragraph}>
                  A new question has been submitted for {productName}. Your expertise is needed!
                </Text>
              </Column>
            </Row>

            <Row style={boxInfos}>
              <Column>
                <Heading as="h2" style={subHeading}>
                  How to Help
                </Heading>
                <Text style={paragraph}>
                  To answer this question:
                </Text>
                <ol style={list}>
                  <li>Click the button below to go to the answer page</li>
                  <li>Read the question carefully</li>
                  <li>Provide a clear and helpful answer</li>
                  <li>Submit your response</li>
                </ol>
              </Column>
            </Row>

            <Row style={containerButton}>
              <Column>
                <Button href={answerPageUrl}>
                  Answer Question
                </Button>
              </Column>
            </Row>
          </Section>

          <Section style={footer}>
            <Text style={footerText}>
              © 2024 Ambassador Program. All rights reserved.
            </Text>
            <Text style={footerText}>
              Thank you for being a valued product ambassador!
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default AmbassadorNewQuestion;

const main = {
  backgroundColor: "#f0f4f8",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  width: "580px",
};

const header = {
  backgroundColor: "#1976D2",
  borderRadius: "4px 4px 0 0",
  padding: "24px",
  textAlign: "center" as const,
};

const content = {
  backgroundColor: "#ffffff",
  borderRadius: "0 0 4px 4px",
  boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
};

const boxInfos = {
  padding: "24px 32px",
};

const mainHeading = {
  color: "#ffffff",
  fontSize: "28px",
  fontWeight: "bold",
  lineHeight: "1.3",
  margin: "0",
};

const subHeading = {
  color: "#1565C0",
  fontSize: "22px",
  fontWeight: "bold",
  lineHeight: "1.3",
  margin: "0 0 16px",
};

const paragraph = {
  color: "#333333",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 16px",
};

const list = {
  ...paragraph,
  paddingLeft: "24px",
};

const containerButton = {
  textAlign: "center" as const,
  padding: "24px 0",
};

const button = {
  backgroundColor: "#1976D2",
  borderRadius: "4px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "bold",
  padding: "12px 24px",
  textDecoration: "none",
  textTransform: "uppercase",
};

const footer = {
  color: "#8c8c8c",
  fontSize: "12px",
  lineHeight: "1.5",
  textAlign: "center" as const,
  padding: "16px 0",
};

const footerText = {
  margin: "8px 0",
};