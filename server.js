import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import https from 'https';
import http from 'http';
import cors from 'cors';

const app = express();
const httpPort = 80;
const httpsPort = 443;

// Load the certificate and key
const privateKey = fs.readFileSync('key.pem', 'utf8');
const certificate = fs.readFileSync('cert.pem', 'utf8');
const credentials = { key: privateKey, cert: certificate };

app.use(cors()); // Enable CORS

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const dir = 'public/uploads';
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    cb(null, dir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});
const upload = multer({ storage });

app.use(express.json());
app.use(express.static('public'));

app.post('/upload', upload.fields([{ name: 'images', maxCount: 5 }, { name: 'video', maxCount: 1 }]), async (req, res) => {
  const images = req.files['images'] || [];
  const video = req.files['video'] ? req.files['video'][0] : null;
  const reviewText = req.body.reviewText;

  const processedImages = images.map(image => `/uploads/${image.filename}`);
  const processedVideo = video ? `/uploads/${video.filename}` : null;

  res.json({
    message: 'Review data processed and formatted for social media',
    images: processedImages,
    video: processedVideo,
    reviewText,
  });
});

// Create HTTP server
http.createServer(app).listen(httpPort, () => {
  console.log(`HTTP Server running at http://localhost:${httpPort}`);
});

// Create HTTPS server
https.createServer(credentials, app).listen(httpsPort, () => {
  console.log(`HTTPS Server running at https://localhost:${httpsPort}`);
});
