import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
} from "@remix-run/react";

export default function App() {
  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <link rel="preconnect" href="https://cdn.shopify.com/" />
        <link
          rel="stylesheet"
          href="https://cdn.shopify.com/static/fonts/inter/v4/styles.css"
        />
        <script src="https://cdn.tailwindcss.com"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              tailwind.config = {
                theme: {
                  extend: {
                    colors: {
                      border: "hsl(214.3 31.8% 91.4%)",
                      input: "hsl(214.3 31.8% 91.4%)",
                      ring: "hsl(222.2 84% 4.9%)",
                      background: "hsl(0 0% 100%)",
                      foreground: "hsl(222.2 84% 4.9%)",
                      primary: {
                        DEFAULT: "hsl(222.2 47.4% 11.2%)",
                        foreground: "hsl(210 40% 98%)",
                      },
                      secondary: {
                        DEFAULT: "hsl(210 40% 96%)",
                        foreground: "hsl(222.2 84% 4.9%)",
                      },
                      destructive: {
                        DEFAULT: "hsl(0 84.2% 60.2%)",
                        foreground: "hsl(210 40% 98%)",
                      },
                      muted: {
                        DEFAULT: "hsl(210 40% 96%)",
                        foreground: "hsl(215.4 16.3% 46.9%)",
                      },
                      accent: {
                        DEFAULT: "hsl(210 40% 96%)",
                        foreground: "hsl(222.2 84% 4.9%)",
                      },
                      popover: {
                        DEFAULT: "hsl(0 0% 100%)",
                        foreground: "hsl(222.2 84% 4.9%)",
                      },
                      card: {
                        DEFAULT: "hsl(0 0% 100%)",
                        foreground: "hsl(222.2 84% 4.9%)",
                      },
                    },
                  },
                },
              }
            `,
          }}
        />
        <Meta />
        <Links />
      </head>
      <body>
        <Outlet />
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}
