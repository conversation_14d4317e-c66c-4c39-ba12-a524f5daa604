import { authenticate } from "~/shopify.server";
import nodemailer from 'nodemailer';

// Interface for email options
interface EmailOptions {
  to: string;
  subject: string;
  html: string;
}

// Using Shopify's Email API
export async function sendShopifyEmail(request: Request ,options: EmailOptions) {
  const { admin } = await authenticate.admin(request);

  try {
    const response = await admin.graphql(`
      mutation emailCreate($to: String!, $subject: String!, $body: String!) {
        emailCreate(
          to: $to
          subject: $subject
          body: $body
          from: "<EMAIL>"
          type: CUSTOMER_ACCOUNT
        ) {
          email {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      variables: {
        to: options.to,
        subject: options.subject,
        body: options.html,
      },
    });

    const responseJson = await response.json();
    
    if (responseJson.data.emailCreate.userErrors.length > 0) {
      console.error("Shopify email error:", responseJson.data.emailCreate.userErrors);
      throw new Error(responseJson.data.emailCreate.userErrors[0].message);
    }

    return responseJson.data.emailCreate.email.id;
  } catch (error) {
    console.error("Failed to send Shopify email:", error);
    throw error;
  }
}

// Using Nodemailer with store's SMTP settings
export async function sendSMTPEmail(request: Request, options: EmailOptions) {
  const { admin } = await authenticate.admin(request);
  
  try {
    // First, fetch the store email settings
    const response = await admin.graphql(`
      query getShopEmail {
        shop {
          email
          customerEmail
        }
      }
    `);
    
    const responseJson = await response.json();
    const shopEmail = responseJson.data.shop.customerEmail || responseJson.data.shop.email;

    // Create Nodemailer transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || shopEmail,
        pass: process.env.SMTP_PASSWORD,
      },
    });

    // Send email
    const info = await transporter.sendMail({
      from: `"${process.env.STORE_NAME}" <${shopEmail}>`,
      to: options.to,
      subject: options.subject,
      html: options.html,
    });

    console.log('Email sent:', info.messageId);
    return info.messageId;
  } catch (error) {
    console.error('Failed to send SMTP email:', error);
    throw error;
  }
}

// Main email sending function that can use either method
export async function sendEmail(
  request: Request, 
  to: string, 
  subject: string, 
  html: string,
  method: 'shopify' | 'smtp' = 'shopify' // Default to Shopify's email API
) {
  const options: EmailOptions = {
    to,
    subject,
    html
  };

  try {
    if (method === 'shopify') {
      return await sendShopifyEmail(request, options);
    } else {
      return await sendSMTPEmail(request, options);
    }
  } catch (error) {
    console.error(`Failed to send email using ${method} method:`, error);
    
    // If primary method fails, try the fallback method
    try {
      const fallbackMethod = method === 'shopify' ? 'smtp' : 'shopify';
      console.log(`Attempting fallback email method: ${fallbackMethod}`);
      
      if (fallbackMethod === 'shopify') {
        return await sendShopifyEmail(request, options);
      } else {
        return await sendSMTPEmail(request, options);
      }
    } catch (fallbackError) {
      console.error('Fallback email method also failed:', fallbackError);
      throw new Error('All email sending methods failed');
    }
  }
}