import { json } from "@remix-run/node";
import AmbassadorConfirmationEmail from "~/emails/ambassadorConfirmation";
import { render } from "@react-email/components";
import { Resend } from "resend";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();
const resend = new Resend('re_2gDMUGdu_GNd7XuEKEPB27HpZw8KeBYAK');

const CUSTOMER_QUERY = `
  query getCustomer($id: ID!) {
    customer(id: $id) {
      id
      firstName
      lastName
      email
    }
  }
`;

export async function handleSaveAmbassador(admin: any, request: any){
    const { custID } = await request.json();
        if (!custID) {
          return json({ error: "Customer ID is required" }, { status: 400 });
        }
        const response = await admin.graphql(
          CUSTOMER_QUERY,
          {
            variables: {
              id: `gid://shopify/Customer/${custID}`
            }
          }
        );
  
        const responseJson = await response.json();
        const customer = responseJson.data.customer;
        const ambassadorName = customer.firstName  + "" + customer.lastName; 
        const ambassadorEmail = customer.email
        console.log(customer);
  
        if (!customer) {
          return json({ error: 'Customer not found' }, { status: 404 });
        }
        else{
          // Save the ambassador details
          const saveAmbassador = await prisma.ambassador.create({
            data : {
              id : custID,
              name: ambassadorName,
              email: ambassadorEmail,
            },
          });
  
          // Send Email
          const emailHtml = render(
            <AmbassadorConfirmationEmail 
              userFirstName={ambassadorName}
            />
          );
          console.log(`Sending email to ${ambassadorEmail}`);
          const {data,error} = await resend.emails.send({
            from: 'REMIX <<EMAIL>>',
            to: ambassadorEmail,
            subject: 'Invitation to Our Ambassador Program',
            html: emailHtml
          });
          if(error){
            return json({ error }, 400)
          }
          console.log(data);
          return json({ message: "Ambassador registered successfully"}, {status: 200});
        }
}