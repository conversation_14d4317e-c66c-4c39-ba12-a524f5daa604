import '@types/jest';
import { uploadToStorage } from '../storage';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

// Mock S3Client
jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn(),
  PutObjectCommand: jest.fn(),
}));

// Mock fs promises
jest.mock('fs/promises', () => ({
  writeFile: jest.fn(),
  mkdir: jest.fn(),
}));

// Mock fs
jest.mock('fs', () => ({
  existsSync: jest.fn(),
}));

describe('Storage Utils', () => {
  let mockS3Client: jest.Mocked<S3Client>;
  
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Setup S3 mock
    mockS3Client = {
      send: jest.fn().mockResolvedValue({}),
    } as any;
    (S3Client as jest.Mock).mockImplementation(() => mockS3Client);
  });

  describe('uploadToStorage', () => {
    it('should upload to S3 when S3 client is available', async () => {
      // Mock environment variables
      process.env.AWS_ACCESS_KEY_ID = 'test-key';
      process.env.AWS_SECRET_ACCESS_KEY = 'test-secret';
      
      const mockFile = new Blob(['test'], { type: 'video/webm' });
      const mockCustID = 'test-customer';
      
      await uploadToStorage(mockFile, undefined, mockCustID);
      
      expect(PutObjectCommand).toHaveBeenCalledWith(expect.objectContaining({
        Bucket: expect.any(String),
        Key: expect.stringContaining('test-customer'),
        ContentType: 'video/webm',
        ACL: 'public-read',
      }));
    });

    it('should fall back to local storage when S3 is not available', async () => {
      // Remove S3 credentials
      delete process.env.AWS_ACCESS_KEY_ID;
      delete process.env.AWS_SECRET_ACCESS_KEY;
      
      const mockFile = new Blob(['test'], { type: 'video/webm' });
      
      await uploadToStorage(mockFile);
      
      // Verify local storage was used
      const { writeFile } = require('fs/promises');
      expect(writeFile).toHaveBeenCalled();
    });

    it('should throw error for invalid file type', async () => {
      const mockFile = new Blob(['test'], { type: 'invalid/type' });
      
      await expect(uploadToStorage(mockFile)).rejects.toThrow('Invalid file type');
    });

    it('should throw error for file size exceeding limit', async () => {
      // Create a large blob (101MB)
      const largeBlob = new Blob([new ArrayBuffer(101 * 1024 * 1024)]);
      
      await expect(uploadToStorage(largeBlob)).rejects.toThrow('File size exceeds maximum limit');
    });
  });
}); 