import { json } from "@remix-run/node";
import { processVideoToReel } from "~/utils/videoProcessor";
import { getSignedVideoUrl } from "~/utils/storage";

export async function action({ request }: { request: Request }) {
  try {
    const { videoUrl, testimonialId } = await request.json();

    if (!videoUrl || !testimonialId) {
      return json({ error: "Missing required parameters" }, { status: 400 });
    }

    // Process the video
    const processedVideoKey = await processVideoToReel(videoUrl, testimonialId);

    // Get a signed URL for the processed video
    const signedUrl = await getSignedVideoUrl(processedVideoKey);

    return json({ url: signedUrl });
  } catch (error) {
    console.error("Error processing video:", error);
    return json({ error: "Failed to process video" }, { status: 500 });
  }
} 