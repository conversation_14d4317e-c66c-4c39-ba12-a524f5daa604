{"name": "realfluence", "private": true, "scripts": {"build": "remix vite:build", "dev": "shopify app dev", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite"}, "type": "module", "engines": {"node": ">=16.0.0 <22.0.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.802.0", "@aws-sdk/s3-request-presigner": "^3.826.0", "@flydotio/litestream": "^1.0.1", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@react-email/components": "^0.0.19", "@remix-run/node": "^2.7.1", "@remix-run/react": "^2.7.1", "@remix-run/serve": "^2.7.1", "@shopify/app-bridge-react": "^4.1.2", "@shopify/polaris": "^12.0.0", "@shopify/shopify-api": "^10.0.0", "@shopify/shopify-app-remix": "^2.8.2", "@shopify/shopify-app-session-storage-prisma": "^4.0.5", "aws-sdk": "^2.1692.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "express": "^4.19.2", "ffmpeg": "^0.0.4", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "isbot": "^5.1.0", "lucide-react": "^0.469.0", "multer": "^1.4.5-lts.1", "openai": "^4.104.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-email": "^2.1.4", "react-router-dom": "^6.30.0", "resend": "^3.2.0", "sharp": "^0.33.4", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vite-tsconfig-paths": "^4.3.1"}, "devDependencies": {"@flydotio/dockerfile": "^0.7.10", "@prisma/client": "^5.20.0", "@remix-run/dev": "^2.16.0", "@remix-run/eslint-config": "^2.7.1", "@shadcn/ui": "^0.0.4", "@shopify/api-codegen-preset": "^0.0.7", "@types/eslint": "^8.40.0", "@types/ffmpeg-static": "^3.0.3", "@types/fluent-ffmpeg": "^2.1.27", "@types/node": "^20.6.3", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.42.0", "eslint-config-prettier": "^9.1.0", "postcss": "^8.5.6", "prettier": "^3.2.4", "prisma": "^5.20.0", "tailwindcss": "^4.1.10", "typescript": "^5.2.2", "vite": "^5.4.14"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {"undici": "6.13.0"}, "overrides": {"undici": "6.13.0"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "dockerfile": {"litestream": true}}