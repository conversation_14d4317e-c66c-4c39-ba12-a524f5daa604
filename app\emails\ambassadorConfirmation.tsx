import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON><PERSON><PERSON>,
  Head,
  <PERSON>ing,
  Html,
  Preview,
  Row,
  Section,
  Text,
} from "@react-email/components";

interface AmbassadorConfirmationEmailProps {
  userFirstName?: string;
  ambassadorPortalURL?: string;
}

export const AmbassadorConfirmationEmail = ({
  userFirstName = "Ambassador",
  ambassadorPortalURL = "https://example.com/ambassador-portal",
}: AmbassadorConfirmationEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>Welcome to Our Ambassador Program - Your Journey Begins!</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Heading style={mainHeading}>
              Welcome to the Ambassador Program!
            </Heading>
          </Section>

          <Section style={content}>
            <Row style={boxInfos}>
              <Column>
                <Heading style={subHeading}>
                  Congratulations, {userFirstName}!
                </Heading>
                <Text style={paragraph}>
                  Your application has been accepted, and you're now officially part of our 
                  exclusive Ambassador Program. We're excited to have you on board!
                </Text>
              </Column>
            </Row>

            <Row style={boxInfos}>
              <Column>
                <Heading as="h2" style={subHeading}>
                  What's Next?
                </Heading>
                <Text style={paragraph}>
                  Here's what you can expect as a new Ambassador:
                </Text>
                <ul style={list}>
                  <li>Access to our Ambassador Portal</li>
                  <li>Opportunities to answer customer questions and earn rewards</li>
                  <li>Early access to new features and products</li>
                  <li>Regular updates on program activities and opportunities</li>
                </ul>
              </Column>
            </Row>

            <Row style={boxInfos}>
              <Column>
                <Heading as="h2" style={subHeading}>
                  Getting Started
                </Heading>
                <Text style={paragraph}>
                  To begin your journey as an Ambassador:
                </Text>
                <ol style={list}>
                  <li>Log in to the Ambassador Portal using the button below</li>
                  <li>Complete your profile and set your preferences</li>
                  <li>Review the Ambassador guidelines and FAQs</li>
                  <li>Start engaging with the community and earning rewards!</li>
                </ol>
              </Column>
            </Row>

            <Row style={containerButton}>
              <Column>
                <Button href={ambassadorPortalURL}>
                  Access Ambassador Portal
                </Button>
              </Column>
            </Row>
          </Section>

          <Section style={footer}>
            <Text style={footerText}>
              © 2024 Ambassador Program. All rights reserved.
            </Text>
            <Text style={footerText}>
              If you have any questions, please contact our Ambassador support team.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default AmbassadorConfirmationEmail;

const main = {
  backgroundColor: "#f0f4f8",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  width: "580px",
};

const header = {
  backgroundColor: "#1976D2",
  borderRadius: "4px 4px 0 0",
  padding: "24px",
  textAlign: "center" as const,
};

const content = {
  backgroundColor: "#ffffff",
  borderRadius: "0 0 4px 4px",
  boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
};

const boxInfos = {
  padding: "24px 32px",
};

const mainHeading = {
  color: "#ffffff",
  fontSize: "28px",
  fontWeight: "bold",
  lineHeight: "1.3",
  margin: "0",
};

const subHeading = {
  color: "#1565C0",
  fontSize: "22px",
  fontWeight: "bold",
  lineHeight: "1.3",
  margin: "0 0 16px",
};

const paragraph = {
  color: "#333333",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 16px",
};

const list = {
  ...paragraph,
  paddingLeft: "24px",
};

const containerButton = {
  textAlign: "center" as const,
  padding: "24px 0",
};

const button = {
  backgroundColor: "#1976D2",
  borderRadius: "4px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "bold",
  padding: "12px  24px",
  textDecoration: "none",
  textTransform: "uppercase",
};

const footer = {
  color: "#8c8c8c",
  fontSize: "12px",
  lineHeight: "1.5",
  textAlign: "center" as const,
  padding: "16px 0",
};

const footerText = {
  margin: "8px 0",
};