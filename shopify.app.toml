# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "5e08ea721cf5671aa6f3efc696843904"
name = "referral-email-app"
handle = "referral-email-app"
application_url = "https://gl-treatments-royal-abilities.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "quickstart-85bd34ff.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders,write_products,write_discounts,read_discounts"

[auth]
redirect_urls = [
  "https://gl-treatments-royal-abilities.trycloudflare.com/auth/callback",
  "https://gl-treatments-royal-abilities.trycloudflare.com/auth/shopify/callback",
  "https://gl-treatments-royal-abilities.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2024-04"

[app_proxy]
url = "https://gl-treatments-royal-abilities.trycloudflare.com/app/proxy"
subpath = "proxy"
prefix = "apps"

[pos]
embedded = false
