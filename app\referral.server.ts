// models/referral.server.ts
import prisma from '~/db.server';

export const createReferral = async (customerId: string, referralCode: string) => {
  return prisma.referral.create({
    data: {
      customerId,
      referralCode,
    },
  });
};

export const incrementReferredCount = async (referralCode: string) => {
  return prisma.referral.update({
    where: { referralCode },
    data: {
      referredCount: {
        increment: 1,
      },
    },
  });
};

export const trackReferralVisit = async (referralCode: string, ipAddress: string, userAgent: string) => {
  await prisma.referralTrack.create({
    data: {
      referralCode,
      ipAddress,
      userAgent,
    },
  });

  return incrementReferredCount(referralCode);
};
