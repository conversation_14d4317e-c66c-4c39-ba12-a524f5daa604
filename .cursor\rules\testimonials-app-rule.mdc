---
description: Generating new code - This acts like a guide for the project
globs: 
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to


Shopify app description - This app can be installed on a Shopify store to collect testimonials of a product or general experience on the store. The app lets the users share the testimonials to peers and also let's the store owner post them on their own social media. 

Frontend:
[route.tsx](mdc:app/routes/_index/route.tsx) - This is the landing page of the app 
[index.tsx](mdc:app/routes/testimonials/index.tsx) - /testimonials page where the customers will be redirected to record the testimonials

Extensions:
[Checkout.tsx](mdc:extensions/testimonials-form/src/Checkout.tsx) - This file is the UI for "Thank you" page on Shopify store. After each purchase the "eligible" customers are shown the form to choose if they want to provide a testimonial. Eligible means - Have previous orders on the store > 10 (this number should be controlled by Store owner)
This should redirect them to [index.tsx](mdc:app/routes/testimonials/index.tsx) (<appname>.com/testimonials?store=<storename>&custID=<customerID>)


Backend:
[app.proxy.tsx](mdc:app/routes/app.proxy.tsx) - This is the API endpoint file which handles all the backend functions.
[apiClient.ts](mdc:app/routes/apiClient.ts) - This is file used by the frontend to contact app.proxy endpoints.

Admin side:
[app.testimonials.tsx](mdc:app/routes/app.testimonials.tsx) - This page is used by the Shopify store owner to see and manage their product testimonials

DB:
[schema.prisma](mdc:prisma/schema.prisma) - This is the schema for our db