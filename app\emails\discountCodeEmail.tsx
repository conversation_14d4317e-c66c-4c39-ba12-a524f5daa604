import React from "react";
import {
    <PERSON>,
    But<PERSON>,
    Container,
    <PERSON><PERSON><PERSON>,
    Head,
    Heading,
    Html,
    Preview,
    Row,
    Section,
    Text,
} from "@react-email/components";

interface DiscountCodeEmailProps {
    userName: string;
    discountCode: string;
    storeUrl: string;
}

export const DiscountCodeEmail: React.FC<DiscountCodeEmailProps> = ({ 
    userName, 
    discountCode, 
    storeUrl 
}) => {
  return (
    <Html>
      <Head />
      <Preview>Here's your exclusive discount code!</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Heading style={mainHeading}>
              Your Special Discount Code
            </Heading>
          </Section>

          <Section style={content}>
            <Row style={boxInfos}>
              <Column>
                <Text style={paragraph}>
                  Hi {userName},
                </Text>
                <Text style={paragraph}>
                  Thank you for being part of our community! Here's your exclusive discount code:
                </Text>
                <Text style={discountCodestyle}>
                  {discountCode}
                </Text>
                <Text style={paragraph}>
                  This is a one-time use code that you can apply at checkout.
                </Text>
              </Column>
            </Row>

            <Row style={containerButton}>
              <Column>
                <Button href={storeUrl} style={button}>
                  Shop Now
                </Button>
              </Column>
            </Row>
          </Section>

          <Section style={footer}>
            <Text style={footerText}>
              © 2024 Your Store. All rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Email styles
const main = {
  backgroundColor: "#f6f9fc",
  fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "20px 0",
  maxWidth: "600px",
};

const header = {
  padding: "20px",
  borderBottom: "1px solid #e6ebf1",
};

const mainHeading = {
  fontSize: "24px",
  lineHeight: "30px",
  textAlign: "center" as const,
  color: "#333",
};

const content = {
  padding: "20px",
};

const boxInfos = {
  padding: "20px 0",
};

const paragraph = {
  fontSize: "16px",
  lineHeight: "24px",
  color: "#525f7f",
};

const discountCodestyle = {
  fontSize: "24px",
  fontWeight: "bold" as const,
  textAlign: "center" as const,
  padding: "12px",
  margin: "12px 0",
  backgroundColor: "#f0f4f8",
  borderRadius: "4px",
  letterSpacing: "2px",
};

const containerButton = {
  textAlign: "center" as const,
  padding: "20px 0",
};

const button = {
  backgroundColor: "#3869d4",
  borderRadius: "4px",
  color: "#ffffff",
  fontSize: "16px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "12px 16px",
};

const footer = {
  padding: "20px",
  borderTop: "1px solid #e6ebf1",
};

const footerText = {
  fontSize: "12px",
  lineHeight: "16px",
  color: "#8898aa",
  textAlign: "center" as const,
};