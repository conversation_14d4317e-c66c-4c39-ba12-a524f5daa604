// import { type ActionFunctionArgs } from "@remix-run/node";
// import { authenticate } from "../shopify.server";
// import db from "../db.server";
// import { Resend } from "resend";
// import { render } from "@react-email/components";
// import { EmailNew } from "~/emails/new";
// import {json} from "@remix-run/react";

// const resend = new Resend('re_2gDMUGdu_GNd7XuEKEPB27HpZw8KeBYAK');

// const emailHtml = render(<EmailNew url={''}/>)

// export const action = async ({ request }: ActionFunctionArgs) => {
//   const { topic, shop, session, admin, payload } = await authenticate.webhook(request);

//   if (!admin) {
//     // The admin context isn't returned if the webhook fired after a shop was uninstalled.
//     throw new Response();
//   }

//   switch (topic) {
//     case "APP_UNINSTALLED":
//       if (session) {
//         await db.session.deleteMany({ where: { shop } });
//       }
//       case "ORDER_CREATE":
//         console.log("--------hit orders webhook------------")
//         console.log(payload)
//         const {data,error} = await resend.emails.send({
//           from: 'REMIX <<EMAIL>>',
//           to: '<EMAIL>',
//           subject: 'Hello World',
//           html: emailHtml
//       });
  
//       if(error){
//           return json({ error }, 400)
//       }
//       console.log("--------hit orders webhook------------")
//       return json({ data }, 200)

//       break;
//     case "CUSTOMERS_DATA_REQUEST":
//     case "CUSTOMERS_REDACT":
//     case "SHOP_REDACT":
//     default:
//       throw new Response("Unhandled webhook topic", { status: 404 });
//   }

//   throw new Response();
// };
