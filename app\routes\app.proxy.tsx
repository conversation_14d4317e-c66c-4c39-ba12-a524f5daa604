import { ActionFunction, LoaderFunction, json } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { handleSaveAmbassador } from "~/components/handleSaveAmbassador";
import { PrismaClient } from "@prisma/client";
import { uploadToStorage, getSignedVideoUrl } from "~/utils/storage";
import crypto from 'crypto';
import { Resend } from 'resend';
import { DiscountCodeEmail } from "~/emails/discountCodeEmail";

const prisma = new PrismaClient();
const resend = new Resend('re_2gDMUGdu_GNd7XuEKEPB27HpZw8KeBYAK');
const CUSTOMER_QUERY = `
  query getCustomer($id: ID!) {
    customer(id: $id) {
      id
      firstName
      lastName
      email
    }
  }
`;

const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://quickstart-85bd34ff.myshopify.com', // This URL will be changed to the actual URL of the app
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '3600'
};

const addCorsHeaders = (response: Response) => {
  Object.entries(corsHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  return response;
};

function isTimestampValid(timestamp: string): boolean {
  const timestampNum = parseInt(timestamp, 10);
  const now = Math.floor(Date.now() / 1000); // Current time in seconds
  const fiveMinutes = 5 * 60; // 5 minutes in seconds
  
  // Check if timestamp is within 5 minutes of current time
  return Math.abs(now - timestampNum) < fiveMinutes;
}

function validateShopifyHmac(queryParams: URLSearchParams): boolean {
  const signature = queryParams.get('signature');
  const timestamp = queryParams.get('timestamp');
  const secret = '3b90366df10158e80f6e5b65ca1d427c';

  if (!signature || !timestamp || !secret) {
    console.error("Missing signature, timestamp, or secret");
    return false;
  }

  // Validate timestamp first
  if (!isTimestampValid(timestamp)) {
    console.error("Timestamp is outside of tolerance range");
    return false;
  }

  // Rest of your HMAC validation code...
  const params = new URLSearchParams(queryParams);
  params.delete('signature');
  
  const sortedParams = Array.from(params.entries())
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([key, value]) => `${key}=${value}`)
    .join('');

  const hmac = crypto
    .createHmac('sha256', secret)
    .update(sortedParams)
    .digest('hex');

  try {
    return crypto.timingSafeEqual(
      Buffer.from(hmac),
      Buffer.from(signature)
    );
  } catch (error) {
    console.error("HMAC validation error:", error);
    return false;
  }
}

export const action: ActionFunction = async ({ request }) => {
  try {
    console.log("---------------App proxy hit---------------------");
    
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: {
          ...corsHeaders,
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
        }
      });
    }

    const url = new URL(request.url);
    console.log("Request URL:", url.toString());
    console.log("Query Parameters:", Object.fromEntries(url.searchParams));

    // Validate HMAC signature
    if (!validateShopifyHmac(url.searchParams)) {
      console.error("Invalid HMAC signature");
      return addCorsHeaders(
        json({ error: "Invalid signature" }, { status: 401 })
      );
    }

    const { admin } = await authenticate.public.appProxy(request);
    const action = url.searchParams.get('action');
    let response;
    
    switch(action) {
      case 'getCustomerID':
        response = await getCustomerID(request, admin);
        break;
      case 'saveAmbassador':
        response = await handleSaveAmbassador(admin, request);
        break;
      case 'submitVideo':
        console.log("Inside the if block")
        response = await handleSubmitVideo(request);
        break;
      case 'getCustomerProducts':
        response = await handleGetCustomerProducts(request, admin);
        break;
      case 'fetchProductTestimonials':
        response = await fetchProductTestimonials(request);
        break;
      case 'createDiscountCode':
        response = await createDiscountCode(request, admin);
        break;
      default:
        response = json({ error: "Invalid action for POST request" }, { status: 400 });
    }

    return addCorsHeaders(response);
  } catch (error) {
    console.error("Error in action function:", error);
    return addCorsHeaders(
      json({ error: "An error occurred while processing your request" }, { status: 500 })
    );
  }
};

async function handleGetCustomerProducts(request: Request, admin: any) {
  try {
    const { custID } = await request.json();
    const response = await admin.graphql(`
      query getCustomerOrders($customerId: ID!) {
        customer(id: $customerId) {
          lastOrder {
            lineItems(first: 50) {
              edges {
                node {
                  product {
                    id
                    title
                    featuredImage {
                      url
                    }
                  }
                }
              }
            }
          }
        }
      }
    `, {
      variables: {
        customerId: `gid://shopify/Customer/${custID}`
      }
    });

    console.log("handleGetCustomerProducts response");
    const data = await response.json();

    if (data.errors) {
      throw new Error(data.errors[0].message);
    }

    if (!data.data?.customer?.lastOrder?.lineItems?.edges) {
      return json([]);
    }

    const products = new Map();

    data.data.customer.lastOrder.lineItems.edges.forEach((item: any) => {
      if (item.node.product) {
        const product = item.node.product;
        products.set(product.id, {
          id: product.id.split('/').pop(),
          name: product.title,
          imageUrl: product.featuredImage?.url
        });
      }
    });

    console.log("handleGetCustomerProducts completed");
    return json(Array.from(products.values()));
  } catch (error) {
    console.error('Error fetching customer products:', error);
    return json({ error: 'Failed to fetch products' }, { status: 500 });
  }
}

async function handleSubmitVideo(request: Request) {
  try {
    console.log("handleSubmitVideo triggered");
    const formData = await request.formData();
    console.log("formData", formData);
    const video = formData.get('video');
    const storeDomain = formData.get('storeDomain') as string;
    const custID = formData.get('custID') as string;
    const productId = formData.get('productID') as string;
    const productName = formData.get('productName') as string || "TestProduct";
    const customerName = formData.get('customerName') as string || "Testname";

    if (!video || !custID || !productId || !storeDomain) {
      return json({ error: 'Missing required fields' }, { status: 400 });
    }

    if (!(video instanceof Blob)) {
      console.log("Failed 2")
      return json({ error: 'Invalid video format' }, { status: 400 });
    }

    // Save video to S3 (or local, depending on env)
    // S3 key format: testimonials/{storeDomain}/{productId}/{custID}_{timestamp}.mp4
    const timestamp = Date.now();
    const s3Key = `testimonials/${storeDomain}/${productId}/${custID}_${timestamp}.mp4`;
    console.log(s3Key)
    const videoUrl = await uploadToStorage(video, s3Key);
    console.log(videoUrl)

    const testimonial = await prisma.testimonial.create({
      data: {
        videoUrl, // S3 or local URL
        shop: storeDomain,
        customerId: custID,
        productId: productId,
        createdAt: new Date(),
        customerName: customerName,
        productName: productName,
      }
    });

    return json({ success: true, testimonial });
  } catch (error) {
    console.error('Video submission error:', error);
    return json({ error: 'Failed to save video' }, { status: 500 });
  }
}

async function fetchProductTestimonials(request: Request) {
  try {
    const { productId, store } = await request.json();

    if (!productId) {
      return json({ error: "Missing product ID" }, { status: 400 });
    }
    if (!store){
      return json({error: "Missing store"}, {status:400});
    }

    const testimonials = await prisma.testimonial.findMany({
      where: {
        productId: productId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Generate signed URLs for each testimonial
    const formatted = await Promise.all(
      testimonials.map(async (t) => {
        try {
          const signedUrl = await getSignedVideoUrl(t.videoUrl);
          return {
            id: t.id,
            videoUrl: signedUrl,
            createdAt: t.createdAt,
            customerName: t.customerName,
            productName: t.productName,
          };
        } catch (error) {
          console.error(`Failed to generate signed URL for testimonial ${t.id}:`, error);
          // Keep original URL as fallback
          return {
            id: t.id,
            videoUrl: t.videoUrl,
            createdAt: t.createdAt,
            customerName: t.customerName,
            productName: t.productName,
          };
        }
      })
    );

    return json(formatted);
  } catch (error) {
    console.error('Error fetching testimonials:', error);
    return json({ error: 'Failed to fetch testimonials' }, { status: 500 });
  }
}

async function createDiscountCode(request: Request, admin: any) {
  try {
    const { custID } = await request.json();
    
    if (!custID) {
      return json({ error: 'Missing customer ID' }, { status: 400 });
    }

    console.log(custID);

    // Fetch customer details
    const customerResponse = await admin.graphql(
      CUSTOMER_QUERY,
      {
        variables: {
          id: `gid://shopify/Customer/${custID}`
        }
      }
    );
    
    const customerData = await customerResponse.json();
    
    if (customerData.errors) {
      throw new Error(customerData.errors[0].message);
    }
    
    const customer = customerData.data.customer;
    
    if (!customer) {
      return json({ error: 'Customer not found' }, { status: 404 });
    }

    // Generate a unique discount code
    const discountCode = `AMBASSADOR-${crypto.randomBytes(3).toString('hex').toUpperCase()}`;
    console.log(discountCode)
    // Create the discount using the GraphQL API
    const discountResponse = await admin.graphql(`
      mutation CreateDiscountCode($basicCodeDiscount: DiscountCodeBasicInput!) {
        discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
          codeDiscountNode {
            id
            codeDiscount {
              ... on DiscountCodeBasic {
                title
                startsAt
                endsAt
                customerSelection {
                  ... on DiscountCustomers {
                    customers {
                      id
                    }
                  }
                }
                customerGets {
                  value {
                    ... on DiscountPercentage {
                      percentage
                    }
                  }
                }
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      variables: {
        basicCodeDiscount: {
          title: `Ambassador Discount for ${customer.firstName} ${customer.lastName}`,
          code: discountCode,
          startsAt: new Date().toISOString(),
          endsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
          customerSelection: {
            customers: {
              add: [`gid://shopify/Customer/${custID}`]
            }
          },
          customerGets: {
            value: {
              percentage: 0.15 // 15% discount
            },
            items: {
              all: true
            }
          },
          appliesOncePerCustomer: true,
          usageLimit: 1
        }
      }
    });

    const discountData = await discountResponse.json();
    
    if (discountData.errors || (discountData.data?.discountCodeBasicCreate?.userErrors?.length > 0)) {
      const errorMessage = discountData.errors?.[0]?.message || 
                           discountData.data?.discountCodeBasicCreate?.userErrors?.[0]?.message || 
                           'Failed to create discount code';
      throw new Error(errorMessage);
    }

    // Get the shop URL from the request parameters
    const url = new URL(request.url);
    const shop = url.searchParams.get('shop');
    if (!shop) {
      throw new Error('Shop parameter is missing');
    }
    const storeUrl = `https://${shop}`;

    // Send email with discount code
    const { data, error } = await resend.emails.send({
      from: 'Your Store <<EMAIL>>',
      to: customer.email,
      subject: 'Your Exclusive Ambassador Discount Code',
      react: (
        <DiscountCodeEmail 
          userName={customer.firstName} 
          discountCode={discountCode} 
          storeUrl={storeUrl} 
        />
      )
    });

    if (error) {
      console.error('Email sending error:', error);
      return json({ 
        success: true, 
        discountCode, 
        warning: 'Discount code created but email could not be sent' 
      });
    }

    return json({ 
      success: true, 
      discountCode, 
      emailSent: true, 
      messageId: data?.id 
    });
  } catch (error) {
    console.error('Error creating discount code:', error);
    return json({ error: 'Failed to create discount code' }, { status: 500 });
  }
}

async function getCustomerID(request: Request, admin: any) {
  try {
    const { custEmail } = await request.json();
    
    if (!custEmail) {
      return json({ error: 'Email is required' }, { status: 400 });
    }

    // Query Shopify's GraphQL API to find customer by email
    const response = await admin.graphql(`
      query getCustomerByEmail($email: String!) {
        customers(first: 1, query: $email) {
          edges {
            node {
              id
              firstName
              lastName
              email
            }
          }
        }
      }
    `, {
      variables: {
        email: custEmail
      }
    });

    console.log("response", response)
    const data = await response.json();

    if (data.errors) {
      throw new Error(data.errors[0].message);
    }

    const customers = data.data?.customers?.edges;
    
    if (!customers || customers.length === 0) {
      return json({ error: 'Customer not found' }, { status: 404 });
    }

    // Extract the customer ID from the Shopify GID
    const customerId = customers[0].node.id.split('/').pop();

    return json({ 
      id: customerId,
      firstName: customers[0].node.firstName,
      lastName: customers[0].node.lastName,
      email: customers[0].node.email
    });
  } catch (error) {
    console.error('Error fetching customer ID:', error);
    return json({ error: 'Failed to fetch customer ID' }, { status: 500 });
  }
}