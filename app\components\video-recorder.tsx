import { useState, useRef, useCallback, useEffect } from "react"
import { But<PERSON> } from "../components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../components/ui/card"
import { ArrowLeft, Video, VideoOff, RotateCcw, Lightbulb, Camera, Play } from "lucide-react"

interface VideoRecorderProps {
  onRecordingComplete: (blob: Blob) => void
  questions: string[]
  productName: string
  onBack?: () => void
}

const sampleTestimonials = [
  "I absolutely love this snowboard! The quality exceeded my expectations and it performs amazingly on the slopes. The design is sleek and it's perfect for both beginners and experienced riders.",
  "This product has completely changed my snowboarding experience. The craftsmanship is top-notch and I've received so many compliments. Highly recommend to anyone looking for quality gear!",
  "What surprised me most was how lightweight yet durable this snowboard is. I've been using it all season and it still looks brand new. Great value for money!",
]

export function VideoRecorder({ onRecordingComplete, questions, productName, onBack }: VideoRecorderProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [recordedChunks, setRecordedChunks] = useState<Blob[]>([])
  const [hasRecorded, setHasRecorded] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [hasPermission, setHasPermission] = useState(false)
  const [agreed, setAgreed] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [uploadedVideo, setUploadedVideo] = useState<Blob | null>(null)
  const [uploadMode, setUploadMode] = useState<'record' | 'upload'>('record')
  const [isProcessingUpload, setIsProcessingUpload] = useState(false)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      setHasRecorded(true)
      if (videoRef.current && videoRef.current.srcObject instanceof MediaStream) {
        videoRef.current.srcObject.getTracks().forEach((track) => track.stop())
      }
    }
  }, [])

  const requestPermission = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true })
      setHasPermission(true)
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
    } catch (error) {
      console.error("Error accessing media devices:", error)
      setHasPermission(false)
    }
  }, [])

  // Auto-request camera permission on mount
  useEffect(() => {
    requestPermission()
  }, [requestPermission])

  // Timer effect
  useEffect(() => {
    if (isRecording) {
      setRecordingTime(0)
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => {
          const newTime = prev + 1
          if (newTime >= 30) { // 30 seconds max
            stopRecording()
            return 30
          }
          return newTime
        })
      }, 1000)
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [isRecording, stopRecording])

  const startRecording = useCallback(async () => {
    try {
      let stream = null
      if (videoRef.current && videoRef.current.srcObject) {
        stream = videoRef.current.srcObject as MediaStream
      } else {
        stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true })
        setHasPermission(true)
        if (videoRef.current) {
          videoRef.current.srcObject = stream
        }
      }

      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder

      setRecordedChunks([]) // Clear previous recordings

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          setRecordedChunks((prev) => [...prev, event.data])
        }
      }

      mediaRecorder.onstop = () => {
        setHasRecorded(true)
      }

      mediaRecorder.start()
      setIsRecording(true)
    } catch (error) {
      console.error("Error accessing media devices:", error)
      setHasPermission(false)
    }
  }, [])



  const handleSubmitVideo = useCallback(() => {
    if (uploadMode === 'upload' && uploadedVideo && agreed) {
      setSubmitted(true)
      onRecordingComplete(uploadedVideo)
    } else if (uploadMode === 'record' && recordedChunks.length && agreed) {
      const blob = new Blob(recordedChunks, { type: "video/webm" })
      setSubmitted(true)
      onRecordingComplete(blob)
    }
  }, [recordedChunks, uploadedVideo, uploadMode, onRecordingComplete, agreed])

  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('video/')) {
      alert('Please select a valid video file.')
      return
    }

    // Validate file size (100MB max)
    const maxSize = 100 * 1024 * 1024 // 100MB
    if (file.size > maxSize) {
      alert('File size must be less than 100MB.')
      return
    }

    setIsProcessingUpload(true)
    try {
      // Convert File to Blob
      const blob = new Blob([file], { type: file.type })
      setUploadedVideo(blob)
      setHasRecorded(true)
      setUploadMode('upload')

      // Set up video preview
      if (videoRef.current) {
        const url = URL.createObjectURL(blob)
        videoRef.current.src = url
        videoRef.current.load()
      }
    } catch (error) {
      console.error('Error processing uploaded file:', error)
      alert('Error processing the uploaded file. Please try again.')
    } finally {
      setIsProcessingUpload(false)
    }
  }, [])

  const handleUploadClick = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  const resetToRecord = useCallback(() => {
    setUploadMode('record')
    setUploadedVideo(null)
    setHasRecorded(false)
    setRecordedChunks([])
    setAgreed(false)
    if (videoRef.current) {
      videoRef.current.src = ''
      videoRef.current.srcObject = null
    }
  }, [])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="flex items-center mb-6">
        {onBack && (
          <Button onClick={onBack} className="mr-4 p-2 bg-transparent hover:bg-gray-100">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        )}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Record Your Testimonial</h1>
          <p className="text-gray-600 mt-1">This helps others like you discover the right products.</p>
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Video Recording Section */}
        <div className="lg:col-span-2">
          <Card className="mb-6">
            <CardContent className="p-6">
              {/* Camera Feed */}
              <div className="aspect-video bg-black rounded-lg mb-6 relative overflow-hidden">
                <video ref={videoRef} autoPlay muted playsInline className="w-full h-full object-cover" />
                {!hasPermission && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
                    <div className="text-white text-center">
                      <Camera className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>Click "Enable Camera" to start</p>
                    </div>
                  </div>
                )}
                {isRecording && (
                  <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                    <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                    Recording
                  </div>
                )}
                {hasRecorded && (
                  <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    ✓ Recording Complete
                  </div>
                )}
              </div>

              {/* Recording Controls */}
              <div className="flex justify-center space-x-4">
                {!hasRecorded ? (
                  !isRecording ? (
                    !hasPermission ? (
                      <Button
                        onClick={requestPermission}
                        className="bg-red-500 hover:bg-red-600 text-white px-8 py-3 text-lg"
                      >
                        <Camera className="h-5 w-5 mr-2" />
                        Enable Camera
                      </Button>
                    ) : (
                      <Button
                        onClick={startRecording}
                        disabled={!agreed}
                        className={`px-8 py-3 text-lg ${
                          !agreed
                            ? "bg-gray-400 text-white cursor-not-allowed"
                            : "bg-red-500 hover:bg-red-600 text-white"
                        }`}
                      >
                        <Video className="h-5 w-5 mr-2" />
                        Start Recording
                      </Button>
                    )
                  ) : (
                    <Button
                      onClick={stopRecording}
                      className="bg-red-500 hover:bg-red-600 text-white px-8 py-3 text-lg"
                    >
                      <VideoOff className="h-5 w-5 mr-2" />
                      Stop Recording
                    </Button>
                  )
                ) : (
                  <>
                    <Button
                      onClick={() => {
                        setHasRecorded(false)
                        setRecordedChunks([])
                        // Don't reset agreed state so user doesn't have to check again
                      }}
                      className="px-6 py-3 bg-white text-black border border-black hover:bg-gray-50"
                    >
                      <RotateCcw className="h-5 w-5 mr-2" />
                      Retake
                    </Button>
                    <Button
                      onClick={handleSubmitVideo}
                      disabled={!agreed}
                      className={`px-8 py-3 text-lg ${
                        !agreed
                          ? "bg-gray-400 text-white cursor-not-allowed"
                          : "bg-green-500 hover:bg-green-600 text-white"
                      }`}
                    >
                      Submit Video
                    </Button>
                  </>
                )}
              </div>

              {/* Consent Checkbox */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="consent"
                    checked={agreed}
                    onChange={(e) => setAgreed(e.target.checked)}
                    className="mt-1 w-4 h-4 text-gray-900 border-gray-300 rounded focus:ring-gray-900 focus:ring-2"
                  />
                  <label htmlFor="consent" className="text-sm font-medium text-gray-900 cursor-pointer">
                    <span className="font-bold">✅ I give permission for this video to be shared publicly.</span>
                    <p className="text-gray-600 mt-1">
                      By recording, you agree to our Privacy Policy and allow us to use your testimonial for marketing
                      purposes.
                    </p>
                  </label>
                </div>
                {hasPermission && !agreed && (
                  <div className="mt-3 text-sm text-orange-600 bg-orange-50 p-2 rounded">
                    ⚠️ Please check the consent box above to start recording
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          {/* Need Inspiration Section - Below recording area */}
          {!hasRecorded && (
            <Card className="mt-8">
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <Lightbulb className="h-5 w-5 mr-2 text-yellow-500" />
                  Need inspiration?
                </CardTitle>
                <p className="text-sm text-gray-600">Watch these sample testimonials to get ideas for your own video</p>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-4">
                  {sampleTestimonials.map((sample, index) => (
                    <div key={index} className="space-y-3">
                      <div className="aspect-video bg-gray-900 rounded-lg relative overflow-hidden">
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-white text-center">
                            <Play className="h-8 w-8 mx-auto mb-2" />
                            <p className="text-sm">Sample {index + 1}</p>
                          </div>
                        </div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm text-gray-700">"{sample}"</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Tips and Samples Section */}
        {!hasRecorded && (
          <div className="space-y-6">
            {/* Tips for Great Testimonial */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Tips for a Great Testimonial</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm space-y-2">
                  {questions.map((tip, index) => (
                    <p key={index}>
                      • <strong>{tip}</strong>
                    </p>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Instructions */}
            <Card>
              <CardContent className="pt-6">
                <div className="text-sm text-gray-600 space-y-2">
                  <p>
                    <strong>Before recording:</strong>
                  </p>
                  <p>• Click 'Start Recording' and speak freely</p>
                  <p>• You can re-record if needed</p>
                  <p>• Tell us what you loved, what surprised you, or how you use the product</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

