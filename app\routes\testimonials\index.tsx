import { TestimonialForm } from "../../components/testimonial-form"
import { useSearchParams } from "@remix-run/react"

export default function TestimonialPage() {
  const [searchParams] = useSearchParams();
  const storeDomain = searchParams.get('storeDomain');
  const custID = searchParams.get('custID');

  if (!storeDomain || !custID) {
    return (
      <div className="max-w-6xl mx-auto px-4 py-8 sm:py-16 flex flex-col items-center">
        <div className="text-center">
          <h1 className="text-2xl sm:text-4xl font-extrabold text-gray-900 mb-4 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            Error: Missing Required Parameters
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Please ensure you have a valid URL with storeDomain and custID parameters.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TestimonialForm storeDomain={storeDomain} custID={custID} />
    </div>
  )
}
