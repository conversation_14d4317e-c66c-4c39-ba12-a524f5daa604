// apiClient.ts
//const BASE_URL = 'https://hq-reunion-prayer-catalog.trycloudflare.com/app/proxy';

interface Product {
  id: string
  name: string
  imageUrl: string
}

// Modified ApiResponse interface to better handle success and error cases
interface ApiResponse<T = any> {
  data?: T;
  error?: string;
}

const getShopifyProxyParams = () => {
  // Convert to seconds and stringify
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const shop = 'quickstart-85bd34ff.myshopify.com';
  
  return {
    shop,
    timestamp,
  };
};

// Helper function to convert string to ArrayBuffer
const stringToBuffer = (str: string): ArrayBuffer => {
  const encoder = new TextEncoder();
  return encoder.encode(str).buffer;
};

// Helper function to convert ArrayBuffer to hex string
const bufferToHex = (buffer: ArrayBuffer): string => {
  return Array.from(new Uint8Array(buffer))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
};

const generateSignature = async (params: Record<string, string>): Promise<string> => {
  const secret = '3b90366df10158e80f6e5b65ca1d427c';
  if (!secret) {
    console.error('NEXT_PUBLIC_SHOPIFY_API_SECRET is not set in environment variables');
    // Return a dummy signature in production rather than crashing the app
    return 'missing-signature';
  }

  // Sort params alphabetically
  const sortedParams = Object.entries(params)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([key, value]) => `${key}=${value}`)
    .join('');

  // Generate HMAC using Web Crypto API
  const encoder = new TextEncoder();
  const keyData = encoder.encode(secret);
  const messageData = encoder.encode(sortedParams);

  // Import the secret key
  const cryptoKey = await window.crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  // Sign the message
  const signature = await window.crypto.subtle.sign(
    'HMAC',
    cryptoKey,
    messageData
  );

  // Convert to hex string
  return bufferToHex(signature);
};

const buildUrl = async (action: string, additionalParams: Record<string, string> = {}) => {
  const baseParams = {
    ...getShopifyProxyParams(),
    action,
    ...additionalParams
  };

  const signature = await generateSignature(baseParams);
  const queryParams = new URLSearchParams({
    ...baseParams,
    signature
  });

  return `/app/proxy?${queryParams.toString()}`;
};

// Add default headers with CORS settings
// const defaultHeaders = {
//   'Content-Type': 'application/json',
//   'Origin': 'https://hq-reunion-prayer-catalog.trycloudflare.com',
// };

export const apiClient = {
  async getCustomerID(email: string): Promise<ApiResponse> {
    try {
      const url = await buildUrl('getCustomerID');
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ custEmail: email })
      });

      if (!response.ok) {
        return { error: `HTTP error! status: ${response.status}` };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      console.error('Error fetching customer ID:', error);
      return { error: 'Failed to fetch customer ID' };
    }
  },

  async submitVideo(formData: FormData): Promise<ApiResponse> {
    try {
      const url = await buildUrl('submitVideo');
      const response = await fetch(url, {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        return { error: `HTTP error! status: ${response.status}` };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      console.error('Error submitting video:', error);
      return { error: 'Failed to submit video' };
    }
  },

  async createDiscountCode(custID: string): Promise<ApiResponse> {
    try {
      const { shop } = getShopifyProxyParams();
      const url = await buildUrl('createDiscountCode', { shop });
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ custID })
      });

      if (!response.ok) {
        return { error: `HTTP error! status: ${response.status}` };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      console.error('Error creating discount code:', error);
      return { error: 'Failed to create discount code' };
    }
  },

  async getCustomerProducts(custId: string): Promise<ApiResponse> {
    try {
      const url = await buildUrl('getCustomerProducts');
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ custID: custId })
      });

      if (!response.ok) {
        return { error: `HTTP error! status: ${response.status}` };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      console.error('Error fetching customer products:', error);
      return { error: 'Failed to fetch customer products' };
    }
  },

  async fetchProductTestimonials(productId: string): Promise<ApiResponse> {
    try {
      const url = await buildUrl('fetchProductTestimonials');
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ productId })
      });

      if (!response.ok) {
        return { error: `HTTP error! status: ${response.status}` };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      console.error('Error fetching testimonials:', error);
      return { error: 'Failed to fetch testimonials' };
    }
  }
};
