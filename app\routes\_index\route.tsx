import type { LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Form, useLoaderData } from "@remix-run/react";
import { MessageSquare, Share2, Gift, TrendingUp, ShoppingBag, Users } from 'lucide-react';

import { login } from "../../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);

  if (url.searchParams.get("shop")) {
    throw redirect(`/app?${url.searchParams.toString()}`);
  }

  return json({ showForm: Boolean(login) });
};

export default function App() {
  const { showForm } = useLoaderData<typeof loader>();

  return (
    <div className="min-h-screen w-full flex items-center justify-center text-center px-4 py-8 sm:px-8 bg-gradient-to-b from-white to-gray-50 text-gray-800">
      <div className="grid gap-12 max-w-6xl mx-auto">
        <div className="flex flex-col items-center max-w-4xl mx-auto">
          <div className="bg-gradient-to-br from-indigo-500 to-purple-600 w-16 h-16 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-indigo-500/30">
            <MessageSquare className="text-white w-8 h-8" />
          </div>
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-extrabold leading-tight mb-6 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            Turn Happy Customers Into Your Best Marketing
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed">
            Boost your Shopify store's organic traffic by collecting authentic video testimonials
            and rewarding customers when they bring in new shoppers.
          </p>
        </div>

        {showForm && (
          <div className="bg-white rounded-xl p-8 shadow-xl shadow-gray-900/10 max-w-2xl mx-auto">
            <Form className="flex flex-col items-center gap-4 w-full" method="post" action="/auth/login">
              <label className="grid gap-2 w-full text-left text-base font-medium">
                <span>Enter your Shopify store</span>
                <input
                  className="px-4 py-3 border border-gray-300 rounded-lg text-base w-full transition-colors focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-500/10"
                  type="text"
                  name="shop"
                  placeholder="your-store.myshopify.com"
                />
              </label>
              <button className="px-6 py-3 bg-gradient-to-br from-indigo-500 to-purple-600 text-white border-none rounded-lg text-base font-semibold cursor-pointer transition-all hover:-translate-y-0.5 hover:shadow-lg hover:shadow-indigo-500/30 w-full mt-2" type="submit">
                Get Started Free
              </button>
            </Form>
            <p className="text-sm text-gray-500 mt-4">No credit card required • Free 14-day trial</p>
          </div>
        )}

        <div className="py-8">
          <h2 className="text-2xl sm:text-3xl font-bold mb-8 text-gray-900">How It Works</h2>

          <ul className="list-none p-0 m-0 grid grid-cols-1 lg:grid-cols-3 gap-8">
            <li className="flex items-start gap-4 text-left bg-white p-6 rounded-xl shadow-md transition-all hover:-translate-y-1 hover:shadow-lg">
              <div className="bg-indigo-100 text-indigo-600 p-3 rounded-lg flex items-center justify-center flex-shrink-0">
                <MessageSquare size={24} />
              </div>
              <div>
                <strong className="block text-lg mb-2 text-gray-900">Collect Video Testimonials</strong>
                <p className="text-gray-600 m-0 leading-relaxed">Easily gather authentic video reviews from your customers right after purchase.</p>
              </div>
            </li>
            <li className="flex items-start gap-4 text-left bg-white p-6 rounded-xl shadow-md transition-all hover:-translate-y-1 hover:shadow-lg">
              <div className="bg-indigo-100 text-indigo-600 p-3 rounded-lg flex items-center justify-center flex-shrink-0">
                <Share2 size={24} />
              </div>
              <div>
                <strong className="block text-lg mb-2 text-gray-900">Social Sharing Made Simple</strong>
                <p className="text-gray-600 m-0 leading-relaxed">Customers can share their shopping experience with friends and family with one click.</p>
              </div>
            </li>
            <li className="flex items-start gap-4 text-left bg-white p-6 rounded-xl shadow-md transition-all hover:-translate-y-1 hover:shadow-lg">
              <div className="bg-indigo-100 text-indigo-600 p-3 rounded-lg flex items-center justify-center flex-shrink-0">
                <Gift size={24} />
              </div>
              <div>
                <strong className="block text-lg mb-2 text-gray-900">Reward Referrals Automatically</strong>
                <p className="text-gray-600 m-0 leading-relaxed">Give discount codes to customers when their referrals make a purchase.</p>
              </div>
            </li>
          </ul>
        </div>

        <div className="py-8">
          <h2 className="text-2xl sm:text-3xl font-bold mb-8 text-gray-900">Benefits For Your Store</h2>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-md text-center transition-all hover:-translate-y-1 hover:shadow-lg">
              <TrendingUp size={32} className="text-indigo-600 mb-4 mx-auto" />
              <h3 className="text-xl font-semibold mb-4 text-gray-900">Increase Organic Traffic</h3>
              <p className="text-gray-600 m-0 leading-relaxed">Drive new visitors through word-of-mouth marketing from your existing customers.</p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-md text-center transition-all hover:-translate-y-1 hover:shadow-lg">
              <ShoppingBag size={32} className="text-indigo-600 mb-4 mx-auto" />
              <h3 className="text-xl font-semibold mb-4 text-gray-900">Boost Conversion Rates</h3>
              <p className="text-gray-600 m-0 leading-relaxed">Authentic testimonials build trust and help convert visitors into buyers.</p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-md text-center transition-all hover:-translate-y-1 hover:shadow-lg">
              <Users size={32} className="text-indigo-600 mb-4 mx-auto" />
              <h3 className="text-xl font-semibold mb-4 text-gray-900">Build Customer Community</h3>
              <p className="text-gray-600 m-0 leading-relaxed">Create a network of brand advocates who bring in their friends and family.</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-12 rounded-xl text-white text-center mt-8">
          <h2 className="text-2xl sm:text-3xl font-bold mb-8 max-w-3xl mx-auto">Ready to grow your Shopify store with customer testimonials?</h2>
          {showForm && (
            <button
              className="px-8 py-4 bg-white text-indigo-600 border-none rounded-lg text-lg font-semibold cursor-pointer transition-all hover:-translate-y-0.5 hover:shadow-lg hover:shadow-black/20"
              onClick={() => {
                document.querySelector('input[name="shop"]')?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              Get Started Today
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
