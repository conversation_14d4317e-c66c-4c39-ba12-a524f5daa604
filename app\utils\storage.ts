import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const UPLOAD_DIR = join(process.cwd(), 'public', 'uploads', 'testimonials');
const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
const ALLOWED_TYPES = ['video/webm', 'video/mp4'];

const S3_BUCKET = process.env.S3_BUCKET || 'testtrustpeer';
const S3_REGION = process.env.S3_REGION || 'us-east-1';
const S3_BASE_URL = `https://${S3_BUCKET}.s3.${S3_REGION}.amazonaws.com/testimonials`;

// const CLOUDFRONT_BASE_URL = 'https://your-cloudfront-domain.com'; // Uncomment and set your CloudFront domain

let s3Client: S3Client | null = null;
if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
  s3Client = new S3Client({
    region: S3_REGION,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
  });
}

export async function uploadToStorage(file: File | Blob, s3Key?: string, custID?: string): Promise<string> {
  if (s3Client) {
    // Upload to S3
    const timestamp = Date.now();
    let filename: string;
    if (file instanceof File) {
      filename = custID ? `${custID}_${timestamp}.mp4` : `${timestamp}-${sanitizeFilename(file.name)}`;
    } else {
      filename = custID ? `${custID}_${timestamp}.mp4` : `${timestamp}-video.webm`;
    }
    const key = s3Key || `testimonials/${filename}`;
    const buffer = Buffer.from(await file.arrayBuffer());
    try {
      await s3Client.send(new PutObjectCommand({
        Bucket: S3_BUCKET,
        Key: key,
        Body: buffer,
        ContentType: file.type,
        // Removed ACL: 'public-read' - we'll use signed URLs instead
      }));
      // Store the S3 key instead of the full URL - we'll generate signed URLs when needed
      return key;
    } catch (error) {
      console.error('S3 upload error:', error);
      throw new Error('Failed to upload video to S3');
    }
  }
  // Fallback to local storage
  if (!existsSync(UPLOAD_DIR)) {
    await mkdir(UPLOAD_DIR, { recursive: true });
  }
  if (file.size > MAX_FILE_SIZE) {
    throw new Error('File size exceeds maximum limit of 100MB');
  }
  if (!ALLOWED_TYPES.includes(file.type)) {
    throw new Error('Invalid file type. Only WebM and MP4 videos are allowed');
  }
  const timestamp = Date.now();
  let filename: string;
  if (file instanceof File) {
    filename = custID ? `${custID}_${timestamp}.mp4` : `${timestamp}-${sanitizeFilename(file.name)}`;
  } else {
    filename = custID ? `${custID}_${timestamp}.mp4` : `${timestamp}-video.webm`;
  }
  const filepath = join(UPLOAD_DIR, filename);
  try {
    const buffer = Buffer.from(await file.arrayBuffer());
    await writeFile(filepath, buffer);
    return `/uploads/testimonials/${filename}`;
  } catch (error) {
    console.error('Upload error:', error);
    throw new Error('Failed to save video file');
  }
}

function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .toLowerCase();
}

// Generate a signed URL for accessing S3 objects
export async function getSignedVideoUrl(s3Key: string, expiresIn: number = 3600): Promise<string> {
  if (!s3Client) {
    // If no S3 client, assume it's a local file path
    return s3Key.startsWith('/') ? s3Key : `/${s3Key}`;
  }

  try {
    const command = new GetObjectCommand({
      Bucket: S3_BUCKET,
      Key: s3Key,
    });

    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn });
    return signedUrl;
  } catch (error) {
    console.error('Error generating signed URL:', error);
    // Fallback to direct S3 URL if signing fails
    return `${S3_BASE_URL}/${s3Key}`;
  }
}

// Helper to generate CloudFront URL (not used yet)
// export function getCloudFrontUrl(s3Key: string): string {
//   return `${CLOUDFRONT_BASE_URL}/${s3Key}`;
// }