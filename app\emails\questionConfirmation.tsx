import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ing,
  Html,
  Preview,
  Row,
  Section,
  Text,
} from "@react-email/components";

interface QuestionSubmissionConfirmationProps {
  userName?: string;
  dashboardURL?: string;
}

export const QuestionSubmissionConfirmation = ({
  userName = "there",
  dashboardURL = "https://example.com/dashboard",
}: QuestionSubmissionConfirmationProps) => {
  return (
    <Html>
      <Head />
      <Preview>Thanks for your question - We'll notify you when it's answered</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Heading style={mainHeading}>
              Question Received
            </Heading>
          </Section>

          <Section style={content}>
            <Row style={boxInfos}>
              <Column>
                <Text style={paragraph}>
                  Hi {userName},
                </Text>
                <Text style={paragraph}>
                  Thanks for asking a question. We've received it and will notify you once an ambassador answers.
                </Text>
              </Column>
            </Row>

            <Row style={boxInfos}>
              <Column>
                <Heading as="h2" style={subHeading}>
                  What's Next?
                </Heading>
                <Text style={paragraph}>
                  Here's what you can expect:
                </Text>
                <ul style={list}>
                  <li>Our product ambassadors will review your question</li>
                  <li>You'll receive an email notification when your question is answered</li>
                  <li>You can always check the status of your question in your account dashboard</li>
                </ul>
              </Column>
            </Row>

            <Row style={containerButton}>
              <Column>
                <Button href={dashboardURL}>
                  View Dashboard
                </Button>
              </Column>
            </Row>
          </Section>

          <Section style={footer}>
            <Text style={footerText}>
              © 2024 Ambassador Program. All rights reserved.
            </Text>
            <Text style={footerText}>
              If you have any issues, please contact our support team.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default QuestionSubmissionConfirmation;

const main = {
  backgroundColor: "#f0f4f8",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  width: "580px",
};

const header = {
  backgroundColor: "#1976D2",
  borderRadius: "4px 4px 0 0",
  padding: "24px",
  textAlign: "center" as const,
};

const content = {
  backgroundColor: "#ffffff",
  borderRadius: "0 0 4px 4px",
  boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
};

const boxInfos = {
  padding: "24px 32px",
};

const mainHeading = {
  color: "#ffffff",
  fontSize: "28px",
  fontWeight: "bold",
  lineHeight: "1.3",
  margin: "0",
};

const subHeading = {
  color: "#1565C0",
  fontSize: "22px",
  fontWeight: "bold",
  lineHeight: "1.3",
  margin: "0 0 16px",
};

const paragraph = {
  color: "#333333",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 16px",
};

const list = {
  ...paragraph,
  paddingLeft: "24px",
};

const containerButton = {
  textAlign: "center" as const,
  padding: "24px 0",
};

const button = {
  backgroundColor: "#1976D2",
  borderRadius: "4px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "bold",
  padding: "12px 24px",
  textDecoration: "none",
  textTransform: "uppercase",
};

const footer = {
  color: "#8c8c8c",
  fontSize: "12px",
  lineHeight: "1.5",
  textAlign: "center" as const,
  padding: "16px 0",
};

const footerText = {
  margin: "8px 0",
};