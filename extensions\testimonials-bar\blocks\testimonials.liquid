{% schema %}
{
  "name": "Product Testimonials",
  "target": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Customer Testimonials"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout Style",
      "options": [
        {
          "value": "grid",
          "label": "Grid View"
        },
        {
          "value": "carousel",
          "label": "Carousel"
        },
        {
          "value": "paginated",
          "label": "Paginated Bar"
        }
      ],
      "default": "paginated"
    },
    {
      "type": "range",
      "id": "max_testimonials",
      "min": 1,
      "max": 12,
      "step": 1,
      "default": 6,
      "label": "Maximum testimonials to display"
    },
    {
      "type": "range",
      "id": "per_page",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 3,
      "label": "Testimonials per page",
      "info": "Number of testimonials to show per page in paginated mode"
    }
  ],

  "stylesheet": "testimonials.css",
  "javascript": "testimonials.js"
}
{% endschema %}

<div class="testimonials-container" 
     data-product-id="{{ product.id }}" 
     data-store-domain="{{ shop.permanent_domain }}"
     data-layout="{{ section.settings.layout }}"
     data-per-page="{{ section.settings.per_page }}"
     data-proxy-url="{{ routes.root_url }}app/proxy">
  
  <div class="testimonials-header">
    <h2>{{ section.settings.title }}</h2>
    <div class="testimonials-loading-spinner" style="display: none;">
      <div class="spinner"></div>
    </div>
  </div>
  
  <div class="testimonials-content">
    <div class="no-testimonials-message" style="display: none;">
      <p>No video testimonials yet. Be the first to share your experience!</p>
    </div>
    
    <div class="testimonials-grid" style="display: none;"></div>
    
    <div class="testimonials-carousel" style="display: none;">
      <button class="carousel-control prev" aria-label="Previous testimonial">&lt;</button>
      <div class="carousel-container"></div>
      <button class="carousel-control next" aria-label="Next testimonial">&gt;</button>
    </div>
    
    <div class="testimonials-paginated" style="display: none;">
      <div class="testimonials-bar"></div>
      <div class="pagination-controls">
        <button class="pagination-button prev" aria-label="Previous page" disabled>&lt;</button>
        <div class="pagination-indicator">
          <span class="current-page">1</span> / <span class="total-pages">1</span>
        </div>
        <button class="pagination-button next" aria-label="Next page">&gt;</button>
      </div>
    </div>
  </div>
</div>