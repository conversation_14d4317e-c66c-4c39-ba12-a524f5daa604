import { json } from "@remix-run/node";
import AmbassadorConfirmationEmail from "~/emails/ambassadorConfirmation";
import { render } from "@react-email/components";
import { Resend } from "resend";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();
const resend = new Resend('re_2gDMUGdu_GNd7XuEKEPB27HpZw8KeBYAK');

export async function handleSendEmail(admin: any, request: any){
  try{

    const ambassadorName = "Testname" // Will be fetched from the list
    const ambassadorEmail = "<EMAIL>"

    // Send Email
    const emailHtml = render(
      <AmbassadorConfirmationEmail 
        userFirstName={ambassadorName}
      />
    );
    console.log(`Sending email to ${ambassadorEmail}`);
    const {data,error} = await resend.emails.send({
      from: 'REMIX <<EMAIL>>',
      to: ambassadorEmail,
      subject: 'Invitation to Our Ambassador Program',
      html: emailHtml
    });
    if(error){
      return json({ error }, 400)
    }

  }
  catch(error){

  }

}
