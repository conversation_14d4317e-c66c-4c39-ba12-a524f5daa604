// File: components/HomePage.tsx
import React from 'react';
import {
  Page,
  Layout,
  Card,
  Text,
  Grid,
  BlockStack,
  InlineStack,
  Icon,
  Button,
  Box,
} from '@shopify/polaris';
import {
  CursorIcon,
  MarketsIcon,
  OrderIcon,
} from '@shopify/polaris-icons';

export function HomePage() {
  return (
    <Page>
      <BlockStack >
        {/* Hero Section */}
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack  align="center">
                <Box width="100%" padding="200">
                  <BlockStack >
                    <Text variant="headingLg" alignment="center" as="h1">
                      Welcome to TrustPeer
                    </Text>
                    <Text as='dd' variant="bodyMd" tone="subdued" alignment="center">
                      Transform your customer testimonials into powerful social proof and drive more sales through authentic recommendations.
                    </Text>
                  </BlockStack>
                </Box>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>

        {/* Getting Started Section */}
        <Layout>
          <Layout.Section>
            <Box padding="200">
              <Text variant="headingMd" as="h2">Getting Started</Text>
            </Box>
          </Layout.Section>
          <Layout.Section>
            <Grid>
              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 6, lg: 6, xl: 6 }}>
                <Card>
                  <BlockStack >
                    <InlineStack  align="start">
                      {/* <Icon source={AnalyticsIcon} /> */}
                      <Text variant="headingSm" as="h3">Analytics Dashboard</Text>
                    </InlineStack>
                    <BlockStack >
                      <Text as='dd'>Get detailed insights into how your testimonials perform:</Text>
                      <Box as="ul" padding="200">
                        <BlockStack>
                          <Text as="dd">Track social media shares and engagement</Text>
                          <Text as="dd">Monitor conversion rates from testimonial views</Text>
                          <Text as="dd">Measure ambassador program impact</Text>
                        </BlockStack>
                      </Box>
                    </BlockStack>
                    <Button variant="primary" url="/app/analytics">View Analytics</Button>
                  </BlockStack>
                </Card>
              </Grid.Cell>
              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 6, lg: 6, xl: 6 }}>
                <Card>
                  <BlockStack >
                    <InlineStack align="start">
                      <Icon source={MarketsIcon} />
                      <Text variant="headingSm" as="h3">Testimonials Hub</Text>
                    </InlineStack>
                    <BlockStack >
                      <Text as='dd'>Your central location for managing testimonials:</Text>
                      <Box as="ul" padding="200">
                        <BlockStack >
                          <Text as="dd">View all product and store testimonials</Text>
                          <Text as="dd">Organize and categorize feedback</Text>
                          <Text as="dd">Easily share across multiple channels</Text>
                        </BlockStack>
                      </Box>
                    </BlockStack>
                    <Button variant="primary" url="/app/testimonials">Manage Testimonials</Button>
                  </BlockStack>
                </Card>
              </Grid.Cell>
            </Grid>
          </Layout.Section>
        </Layout>

        {/* Value Proposition Section */}
        <Layout>
          <Layout.Section>
            <Box padding="200">
              <Text variant="headingMd" as="h2">How TrustPeer Helps You Grow</Text>
            </Box>
          </Layout.Section>
          <Layout.Section>
            <Card>
              <BlockStack >
                <InlineStack align="start">
                  <Icon source={CursorIcon} />
                  <Text variant="headingSm" as="h3">Build Your Ambassador Network</Text>
                </InlineStack>
                <BlockStack >
                  <Text as='dd'>Create a network of trusted customers who authentically promote your products and help convert new buyers through:</Text>
                  <BlockStack >
                    <InlineStack align="start">
                      <Icon source={OrderIcon} />
                      <Text as='dd'>Increased social proof and credibility</Text>
                    </InlineStack>
                    <InlineStack align="start">
                      <Icon source={MarketsIcon} />
                      <Text as='dd'>Direct engagement with potential customers</Text>
                    </InlineStack>
                    <InlineStack align="start">
                      <Icon source={CursorIcon} />
                      <Text as='dd'>Community-driven growth and referrals</Text>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
                <Button variant="primary" url="/app/ambassador">Start Ambassador Program</Button>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>

        {/* How It Works Section */}
        <Layout>
          <Layout.Section>
            <Box padding="200">
              <Text variant="headingMd" as="h2">How It Works</Text>
            </Box>
          </Layout.Section>
          <Layout.Section>
            <Card>
              <BlockStack>
                <Text variant="headingSm" as="h3">Simple 3-Step Process</Text>
                <BlockStack>
                  {[
                    {
                      title: "Select Trusted Customers",
                      description: "Identify your most engaged and satisfied customers. You have complete control over who joins your ambassador program, ensuring quality and authenticity."
                    },
                    {
                      title: "Send Custom Invitations",
                      description: "Send personalized invitations to your selected customers. Our platform makes it easy to craft compelling invites that encourage participation."
                    },
                    {
                      title: "Grow Through Authentic Testimonials",
                      description: "Your ambassadors can easily record video testimonials, share their experiences, and answer questions from potential customers, helping drive more conversions."
                    }
                  ].map((step, index) => (
                    <BlockStack  key={index}>
                      <InlineStack align="start">
                        <Box
                          background="bg-surface-selected"
                          borderRadius="full"
                          width="32px"
                        >
                          <Text variant="bodyMd" as="span">{index + 1}</Text>
                        </Box>
                        <Text variant="headingSm" as="h4">{step.title}</Text>
                      </InlineStack>
                      <Box padding="100">
                        <Text as='dd'>{step.description}</Text>
                      </Box>
                    </BlockStack>
                  ))}
                </BlockStack>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}