import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Head,
  Heading,
  Html,
  Preview,
  Row,
  Section,
  Text,
} from "@react-email/components";

interface AmbassadorProgramEmailProps {
  userFirstName?: string;
  storeURL?: string;
}

export const AmbassadorProgramEmail = ({
  userFirstName = "there",
  storeURL = "https://example.com/ambassador-program"
}: AmbassadorProgramEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>Join Our Exclusive Ambassador Program - Earn Rewards!</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Heading style={mainHeading}>
              Ambassador Program Invitation
            </Heading>
          </Section>

          <Section style={content}>
            <Row style={boxInfos}>
              <Column>
                <Heading style={subHeading}>
                  Welcome, {userFirstName}!
                </Heading>
                <Text style={paragraph}>
                  We're thrilled to invite you to our exclusive Ambassador Program. 
                  Share your expertise, help others, and earn exciting rewards!
                </Text>
              </Column>
            </Row>

            <Row style={boxInfos}>
              <Column>
                <Heading as="h2" style={subHeading}>
                  Program Benefits
                </Heading>
                <ul style={list}>
                  <li>Earn rewards for sharing your product knowledge</li>
                  <li>Get early access to new features and products</li>
                  <li>Influence product development</li>
                  <li>Connect with like-minded enthusiasts</li>
                </ul>
              </Column>
            </Row>

            <Row style={boxInfos}>
              <Column>
                <Heading as="h2" style={subHeading}>
                  How It Works
                </Heading>
                <Text style={paragraph}>
                  As an Ambassador, you'll:
                </Text>
                <ul style={list}>
                  <li>Answer customer questions about products you know well</li>
                  <li>Earn rewards for each helpful answer you provide</li>
                  <li>Participate anonymously - no need to share personal information</li>
                  <li>Help shape our community and improve our products</li>
                </ul>
              </Column>
            </Row>

            <Row style={boxInfos}>
              <Column style={containerButton}>
                <Button href={storeURL}>
                  Join Now
                </Button>
              </Column>
            </Row>
          </Section>

          <Section style={footer}>
            <Text style={footerText}>
              © 2024 Ambassador Program. All rights reserved.
            </Text>
            <Text style={footerText}>
              If you have any questions, please contact our support team.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default AmbassadorProgramEmail;

const main = {
  backgroundColor: "#f0f4f8",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  width: "580px",
};

const header = {
  backgroundColor: "#1976D2",
  borderRadius: "4px 4px 0 0",
  padding: "24px",
  textAlign: "center" as const,
};

const content = {
  backgroundColor: "#ffffff",
  borderRadius: "0 0 4px 4px",
  boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
};

const boxInfos = {
  padding: "24px 32px",
};

const mainHeading = {
  color: "#ffffff",
  fontSize: "28px",
  fontWeight: "bold",
  lineHeight: "1.3",
  margin: "0",
};

const subHeading = {
  color: "#1565C0",
  fontSize: "22px",
  fontWeight: "bold",
  lineHeight: "1.3",
  margin: "0 0 16px",
};

const paragraph = {
  color: "#333333",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 16px",
};

const list = {
  ...paragraph,
  paddingLeft: "24px",
};

const containerButton = {
  textAlign: "center" as const,
  padding: "24px 0",
};

const button = {
  backgroundColor: "#1976D2",
  borderRadius: "4px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "bold",
  padding: "12px 24px",
  textDecoration: "none",
  textTransform: "uppercase",
};

const footer = {
  color: "#8c8c8c",
  fontSize: "12px",
  lineHeight: "1.5",
  textAlign: "center" as const,
  padding: "16px 0",
};

const footerText = {
  margin: "8px 0",
};