# fly.toml app configuration file generated for referral-email-app on 2025-04-03T22:02:47+05:30
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'referral-email-app'
primary_region = 'bom'

[build]

[env]
  PORT = '3000'
  SCOPES = 'read_customers,read_orders,write_discounts,write_products'
  SHOPIFY_API_KEY = '5e08ea721cf5671aa6f3efc696843904'
  SHOPIFY_APP_URL = 'https://referral-email-app.fly.dev'

[processes]
  app = 'node ./dbsetup.js npm run docker-start'

[[mounts]]
  source = 'data'
  destination = '/data'
  auto_extend_size_threshold = 80
  auto_extend_size_increment = '1GB'
  auto_extend_size_limit = '10GB'

[http_service]
  internal_port = 3000
  force_https = true
  auto_start_machines = false
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
