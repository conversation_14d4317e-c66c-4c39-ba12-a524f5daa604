import { useState, useEffect } from "react"
import { But<PERSON> } from "../components/ui/button"
import { Card, CardContent } from "../components/ui/card"
import { VideoRecorder } from "./video-recorder"
import { SocialMediaPreview } from "./social-media-preview"
import { Search } from 'lucide-react'
import { apiClient } from "../routes/apiClient"

interface Product {
  id: string
  name: string
  imageUrl: string
  price?: string
  category?: string
  orderDate?: string
}

interface TestimonialFormProps {
  storeDomain: string;
  custID: string;
}

// Simple custom alert component
const Alert = ({
  children,
  variant = "default",
}: { children: React.ReactNode; variant?: "default" | "destructive" }) => (
  <div className={variant === "destructive"
    ? "p-4 rounded-lg bg-red-50 text-red-800 border border-red-200"
    : "p-4 rounded-lg bg-green-50 text-green-800 border border-green-200"
  }>
    {children}
  </div>
)

export const TestimonialForm: React.FC<TestimonialFormProps> = ({ storeDomain, custID }) => {
  const [selectedProduct, setSelectedProduct] = useState("")
  const [videoBlob, setVideoBlob] = useState<Blob | null>(null)
  const [step, setStep] = useState(1)
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showThankYouMessage, setShowThankYouMessage] = useState(false)
  const [isStoreTestimonial, setIsStoreTestimonial] = useState(false)
  const [showInitialThankYou, setShowInitialThankYou] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  useEffect(() => {
    const fetchProducts = async () => {
      console.log("fetchProducts triggered");
      setIsLoading(true)
      setError(null)
      try {
        const response = await apiClient.getCustomerProducts(custID) // Add param -> storeID // Fetch the customer products for the store.
        if ("error" in response) {
          throw new Error(response.error)
        }
        setProducts(response.data || [])
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred while fetching products")
      } finally {
        setIsLoading(false)
      }
    }

    fetchProducts()
    console.log("fetchProducts completed");
  }, [custID])

  const handleVideoSaved = async (blob: Blob) => {
    const formData = new FormData()
    formData.append('video', blob)
    formData.append('storeDomain', storeDomain)
    formData.append('custID', custID)
    formData.append('productID', selectedProduct)
    // formData.append('productName', productName)
    // formData.append('customerName', customerName)
    

    try {
      const response = await apiClient.submitVideo(formData)
      if ('error' in response) {
        throw new Error(response.error)
      }
      
      // Create discount code
      await apiClient.createDiscountCode(custID)
      
      setVideoBlob(blob)
      setShowInitialThankYou(true)
      setTimeout(() => {
        setShowInitialThankYou(false)
        setStep(3)
      }, 3000)
    } catch (err) {
      alert('Failed to submit testimonial. Please try again.')
      console.error('Error submitting testimonial:', err)
    }
  }

  const handleShare = async () => {
    if (!videoBlob || (!selectedProduct && !isStoreTestimonial)) {
      alert('Please select a product or store testimonial, and record a video before sharing.')
      return
    }

    try {
      setShowThankYouMessage(true)
      setTimeout(() => setShowThankYouMessage(false), 5000)
    } catch (err) {
      alert('Failed to share testimonial. Please try again.')
      console.error('Error sharing testimonial:', err)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="flex flex-col items-center gap-4">
          <div className="w-12 h-12 rounded-full bg-gray-200 animate-pulse"></div>
          <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-3xl mx-auto">
        <Alert variant="destructive">{error}</Alert>
      </div>
    )
  }

  // Filter products based on search query
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (step === 1) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Choose what you'd like to share your experience about
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Select a recent product you purchased or talk about your overall shopping experience.
          </p>
        </div>

        {/* Search Only */}
        <div className="mb-8">
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              placeholder="Search for a product you've bought earlier..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-900 focus:border-transparent"
            />
          </div>
        </div>

        {/* Store Experience Option */}
        <Card className="mb-8 border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">General Store Experience</h3>
                <p className="text-gray-600">Want to share your overall experience with our store?</p>
              </div>
              <Button
                onClick={() => {
                  setIsStoreTestimonial(true)
                  setSelectedProduct("")
                  setStep(2)
                }}
                className="mt-4 md:mt-0 bg-black text-white hover:bg-gray-800"
              >
                Select
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <Card key={product.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                  <img
                    src={product.imageUrl || "/placeholder.svg"}
                    alt={product.name}
                    className="w-full h-full object-cover rounded-lg"
                  />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{product.name}</h3>
                <p className="text-lg font-bold text-gray-900 mb-4">{product.price || "$199.99"}</p>
                <Button
                  onClick={() => {
                    setSelectedProduct(product.id)
                    setIsStoreTestimonial(false)
                    setStep(2)
                  }}
                  className="w-full bg-black text-white hover:bg-gray-800"
                >
                  Select
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No products found matching your search criteria.</p>
          </div>
        )}

        {/* Continue Button for Mobile */}
        <div className="md:hidden fixed bottom-0 left-0 right-0 p-4 bg-white border-t">
          <Button className="w-full bg-black text-white hover:bg-gray-800" disabled>
            Select a product to continue
          </Button>
        </div>
      </div>
    )
  }

  if (step === 2) {
    return (
      <VideoRecorder
        onRecordingComplete={handleVideoSaved}
        productName={isStoreTestimonial ? "Our Store" : (products.find((p) => p.id === selectedProduct)?.name || "This Product")}
        questions={[
          "Speak clearly and naturally",
          "Mention what you love about the product",
          "How has it helped you or solved a problem?",
          "Keep it concise (under 60 seconds)",
          "What specific features do you like?",
        ]}
        onBack={() => setStep(1)}
      />
    )
  }

  if (step === 3 && videoBlob) {
    return (
      <SocialMediaPreview
        productName={isStoreTestimonial ? "our store" : (products.find((p) => p.id === selectedProduct)?.name || "")}
        videoBlob={videoBlob}
        onShareComplete={() => setShowThankYouMessage(true)}
        onShareAnother={() => {
          setStep(1)
          setSelectedProduct("")
          setVideoBlob(null)
          setIsStoreTestimonial(false)
        }}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {showInitialThankYou && (
        <div className="fixed bottom-4 right-4 z-50">
          <Alert>
            <div className="flex items-center gap-3">
              <span>Thank you for providing this testimonial - A 10% discount code for next purchase will be emailed to you :)</span>
            </div>
          </Alert>
        </div>
      )}
    </div>
  )
}
