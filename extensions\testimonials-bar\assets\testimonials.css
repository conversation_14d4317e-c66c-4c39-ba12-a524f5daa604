.testimonials-container {
  margin: 40px 0;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  font-family: var(--font-body-family);
}

.testimonials-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.testimonials-header h2 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

/* Loading Spinner */
.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Grid Layout */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

/* Carousel Layout */
.testimonials-carousel {
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 40px;
}

.carousel-container {
  display: flex;
  flex: 1;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

.carousel-control {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  z-index: 2;
}

.carousel-control.prev { left: 0; }
.carousel-control.next { right: 0; }

/* Paginated Layout */
.testimonials-paginated {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.testimonials-bar {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 20px;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}

.pagination-button {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-button:disabled {
  background: #cbd5e1;
  cursor: not-allowed;
}

.pagination-indicator {
  font-size: 16px;
  color: #4b5563;
}

/* Testimonial Card */
.testimonial-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 15px;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 300px;
  flex: 1;
}

.testimonial-video-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
  margin-bottom: 15px;
}

.testimonial-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  object-fit: cover;
}

.testimonial-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 10px;
}

.testimonial-name {
  font-weight: 600;
  color: #374151;
}

.testimonial-date {
  color: #6b7280;
  font-size: 14px;
}

.no-testimonials-message {
  text-align: center;
  padding: 40px 0;
  color: #6b7280;
}

.testimonial-placeholder {
  background: #000;
  color: white;
  text-align: center;
  padding: 40px 0;
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}

.play-button {
  width: 40px;
  height: 40px;
  background: white;
  mask: url('/assets/play-icon.svg') no-repeat center;
  -webkit-mask: url('/assets/play-icon.svg') no-repeat center;
  mask-size: contain;
  margin-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .testimonials-carousel {
    padding: 0 30px;
  }
  
  .testimonial-card {
    min-width: calc(100% - 20px);
  }
  
  .testimonials-bar {
    flex-direction: column;
  }
}