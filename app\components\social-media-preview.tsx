import { useState, useEffect, useCallback, useRef, forwardRef } from "react"
import { Textarea } from "../components/ui/textarea"
import { Button } from "../components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card"
import { CheckCircle, Facebook, Instagram, Twitter, MessageCircle, Mail, Copy, Play } from "lucide-react"

interface SocialMediaPreviewProps {
  productName: string
  videoBlob: Blob
  onShareComplete?: () => void
  onShareAnother?: () => void
}

type Platform = "twitter" | "facebook" | "whatsapp"

export const SocialMediaPreview = forwardRef<HTMLTextAreaElement, SocialMediaPreviewProps>(
  ({ productName, videoBlob, onShareComplete, onShareAnother }, ref) => {
    const [videoUrl, setVideoUrl] = useState<string>("")
    const [caption, setCaption] = useState("")

    const [shareUrl, setShareUrl] = useState("")
    const [copied, setCopied] = useState(false)
    const videoRef = useRef<HTMLVideoElement>(null)

    const defaultMessage = `I just had an amazing experience with ${productName}! I wanted to share my honest review with everyone. This product has really impressed me with its quality and features. Check out my video review and let me know what you think!`

    useEffect(() => {
      const url = URL.createObjectURL(videoBlob)
      setVideoUrl(url)
      setCaption(defaultMessage)

      // Create a shareable URL (in a real app, this would be a hosted video URL)
      // For demo purposes, we'll use a placeholder
      setShareUrl(`https://yourstore.com/testimonials/share?product=${encodeURIComponent(productName)}`)

      return () => URL.revokeObjectURL(url)
    }, [videoBlob, defaultMessage, productName])



    const copyLink = () => {
      navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }

    const shareToSocial = (platform: string) => {
      const message = caption
      const encodedMessage = encodeURIComponent(message)
      const encodedUrl = encodeURIComponent(shareUrl)

      const urls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedMessage}`,
        instagram: `https://www.instagram.com/`, // Instagram doesn't support direct URL sharing
        twitter: `https://twitter.com/intent/tweet?text=${encodedMessage}&url=${encodedUrl}`,
        whatsapp: `https://wa.me/?text=${encodedMessage}%20${encodedUrl}`,
        email: `mailto:?subject=Check out my product review&body=${encodedMessage}%0A%0A${encodedUrl}`,
      }

      if (platform === "instagram") {
        alert("Please save the video and share it manually on Instagram!")
        return
      }

      window.open(urls[platform as keyof typeof urls], "_blank", "width=600,height=400")

      if (onShareComplete) {
        onShareComplete()
      }
    }



    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Thank You for Your Testimonial!</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            We've received your video and appreciate you taking the time to share your experience with {productName}. Your
            review helps other customers make informed decisions.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">

          {/* Video Preview */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Your Video Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-video bg-black rounded-lg mb-4 relative overflow-hidden">
                  {videoUrl ? (
                    <video
                      src={videoUrl}
                      controls
                      className="w-full h-full object-cover"
                      poster="/placeholder.svg?height=300&width=400"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-white text-center">
                        <Play className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>Video preview</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Video Stats */}
                <div className="flex justify-between text-sm text-gray-500 mb-4">
                  <span>👁️ 0 views</span>
                  <span>❤️ 0 likes</span>
                  <span>💬 0 comments</span>
                </div>

                {/* Custom Message */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Customize Share Message (Optional)
                  </label>
                  <Textarea
                    ref={ref}
                    placeholder={caption}
                    value={caption}
                    onChange={(e) => setCaption(e.target.value)}
                    rows={4}
                    className="resize-none"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sharing Options */}
          <div>
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Spread the Word!</CardTitle>
                <p className="text-sm text-gray-600">Share your video to {productName} with friends and family!</p>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">Here's what your post could look like:</p>

                {/* Preview Post */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6 border">
                  <div className="flex items-center mb-3">
                    <div className="w-8 h-8 bg-gray-300 rounded-full mr-3"></div>
                    <div>
                      <p className="font-medium text-sm">Your Name</p>
                      <p className="text-xs text-gray-500">Just now</p>
                    </div>
                  </div>
                  <p className="text-sm mb-3">{caption}</p>
                  <div className="aspect-video bg-black rounded text-white flex items-center justify-center text-xs">
                    Video Preview
                  </div>
                </div>

                {/* Share Buttons */}
                <div className="space-y-3">
                  <p className="font-medium text-sm mb-3">Share on:</p>

                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      onClick={() => shareToSocial("facebook")}
                      className="bg-blue-600 hover:bg-blue-700 text-white justify-start"
                    >
                      <Facebook className="h-4 w-4 mr-2" />
                      Facebook
                    </Button>

                    <Button
                      onClick={() => shareToSocial("instagram")}
                      className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white justify-start"
                    >
                      <Instagram className="h-4 w-4 mr-2" />
                      Instagram
                    </Button>

                    <Button
                      onClick={() => shareToSocial("twitter")}
                      className="bg-black hover:bg-gray-800 text-white justify-start"
                    >
                      <Twitter className="h-4 w-4 mr-2" />X (Twitter)
                    </Button>

                    <Button
                      onClick={() => shareToSocial("whatsapp")}
                      className="bg-green-500 hover:bg-green-600 text-white justify-start"
                    >
                      <MessageCircle className="h-4 w-4 mr-2" />
                      WhatsApp
                    </Button>
                  </div>

                  <Button
                    onClick={() => shareToSocial("email")}
                    className="w-full justify-start bg-white text-black border-black hover:bg-gray-50"
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Email
                  </Button>

                  <Button
                    onClick={copyLink}
                    className="w-full justify-start bg-white text-black border-black hover:bg-gray-50"
                  >
                    {copied ? (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4 mr-2" />
                        Copy Link
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Incentive Card */}
            <Card className="border-green-200 bg-green-50">
              <CardContent className="pt-6">
                <div className="text-center">
                  <h3 className="font-bold text-green-800 mb-2">🎉 Earn 10% off your next purchase!</h3>
                  <p className="text-sm text-green-700 mb-4">Thank you for being an amazing customer!</p>
                  <p className="text-xs text-green-600">
                    Use code <strong>THANKS10</strong> for 10% off your next order
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
          {onShareAnother && (
            <Button onClick={onShareAnother} className="bg-red-500 hover:bg-red-600 text-white px-8 py-3">
              🎬 Share Another Experience
            </Button>
          )}
          <Button className="px-8 py-3 bg-white text-black border-black hover:bg-gray-50">
            Skip for now
          </Button>
        </div>
      </div>
    )
  },
)

// Add display name for dev tools
SocialMediaPreview.displayName = "SocialMediaPreview"

